# Dual Image Super Resolution Project Structure

## Project Overview
Complete full-stack application for dual satellite image super-resolution with blind quality assessment.

## Directory Structure
```
dual-image-super-resolution/
├── backend/                          # Node.js Backend
│   ├── controllers/
│   │   ├── imageController.js        # Image upload & processing
│   │   └── qualityController.js      # Quality assessment
│   ├── models/
│   │   ├── Image.js                  # MongoDB image schema
│   │   └── ProcessingJob.js          # Job tracking schema
│   ├── routes/
│   │   ├── images.js                 # Image routes
│   │   └── quality.js                # Quality routes
│   ├── middleware/
│   │   ├── upload.js                 # Multer configuration
│   │   └── auth.js                   # Authentication
│   ├── utils/
│   │   ├── pythonBridge.js           # Python script executor
│   │   └── fileManager.js            # File operations
│   ├── uploads/                      # Temporary image storage
│   ├── results/                      # Processed results
│   ├── app.js                        # Express app setup
│   ├── server.js                     # Server entry point
│   └── package.json                  # Dependencies
│
├── python-processing/                # Python Processing Pipeline
│   ├── models/
│   │   ├── dual_srcnn.py             # Dual SRCNN model
│   │   ├── dual_esrgan.py            # Dual ESRGAN model
│   │   └── dual_rcan.py              # Dual RCAN model
│   ├── utils/
│   │   ├── image_registration.py     # Image alignment
│   │   ├── preprocessing.py          # Image preprocessing
│   │   └── postprocessing.py         # Result processing
│   ├── quality_assessment/
│   │   ├── blind_metrics.py          # BRISQUE, NIQE, etc.
│   │   ├── full_reference.py         # PSNR, SSIM, MSE
│   │   └── correlation_analysis.py   # Metric correlation
│   ├── training/
│   │   ├── train_dual_models.py      # Model training scripts
│   │   ├── dataset_loader.py         # Data loading utilities
│   │   └── loss_functions.py         # Custom loss functions
│   ├── inference/
│   │   ├── super_resolve.py          # Main SR inference
│   │   └── quality_evaluate.py       # Quality evaluation
│   ├── weights/                      # Trained model weights
│   ├── main_processor.py             # Main processing script
│   └── requirements.txt              # Python dependencies
│
├── frontend/                         # React.js Frontend
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/
│   │   │   ├── ImageUpload.jsx       # Dual image upload
│   │   │   ├── ProcessingStatus.jsx  # Real-time status
│   │   │   ├── ResultsDisplay.jsx    # Results visualization
│   │   │   ├── QualityMetrics.jsx    # Quality scores display
│   │   │   └── ImageComparison.jsx   # Before/after comparison
│   │   ├── pages/
│   │   │   ├── Home.jsx              # Landing page
│   │   │   ├── Upload.jsx            # Upload interface
│   │   │   ├── Processing.jsx        # Processing page
│   │   │   └── Results.jsx           # Results page
│   │   ├── services/
│   │   │   ├── api.js                # Axios API calls
│   │   │   └── websocket.js          # Real-time updates
│   │   ├── utils/
│   │   │   ├── imageUtils.js         # Image utilities
│   │   │   └── formatters.js         # Data formatters
│   │   ├── styles/
│   │   │   └── globals.css           # Tailwind CSS
│   │   ├── App.jsx                   # Main app component
│   │   └── index.js                  # Entry point
│   ├── package.json                  # Dependencies
│   └── tailwind.config.js            # Tailwind configuration
│
├── dataset/                          # Training & Test Data
│   ├── train/
│   │   ├── lr_pairs/                 # Low-res image pairs
│   │   └── hr_targets/               # High-res targets
│   ├── val/
│   │   ├── lr_pairs/
│   │   └── hr_targets/
│   └── test/
│       ├── lr_pairs/
│       └── hr_targets/
│
├── docs/                             # Documentation
│   ├── API.md                        # API documentation
│   ├── SETUP.md                      # Setup instructions
│   └── MODELS.md                     # Model documentation
│
├── scripts/                          # Utility Scripts
│   ├── setup_env.sh                  # Environment setup
│   ├── download_models.py            # Pre-trained model download
│   └── create_dataset.py             # Dataset preparation
│
├── docker/                           # Docker Configuration
│   ├── Dockerfile.backend
│   ├── Dockerfile.python
│   └── docker-compose.yml
│
├── .env.example                      # Environment variables template
├── .gitignore                        # Git ignore rules
├── README.md                         # Project documentation
└── requirements.txt                  # Global requirements
```

## Technology Stack

### Backend
- **Node.js + Express.js**: RESTful API server
- **MongoDB**: Database for storing image metadata and results
- **Multer**: File upload handling
- **Socket.io**: Real-time processing updates

### Python Processing
- **PyTorch**: Deep learning framework
- **OpenCV**: Image processing and registration
- **Pyiqa**: Blind image quality assessment
- **Scikit-image**: Image metrics (PSNR, SSIM)
- **NumPy/Pandas**: Data processing

### Frontend
- **React.js**: User interface
- **Tailwind CSS**: Styling framework
- **Axios**: HTTP client
- **Socket.io-client**: Real-time updates

## Key Features
1. **Dual Image Upload**: Upload two low-resolution satellite images
2. **Image Registration**: Automatic alignment of input images
3. **Super Resolution**: Multiple deep learning models for SR
4. **Blind Quality Assessment**: No-reference quality evaluation
5. **Real-time Processing**: Live status updates
6. **Results Visualization**: Before/after comparison with metrics
7. **Model Comparison**: Compare different SR approaches
8. **Export Results**: Download processed images and reports
