#!/usr/bin/env python3
"""
Demo script to test SRCNN model implementation
This script demonstrates the basic functionality without requiring actual dataset
"""

import torch
import torch.nn as nn
import numpy as np
from PIL import Image
import os

print("SatelliteSR SRCNN Demo")
print("=" * 40)

# SRCNN Model Definition (from the notebook)
class SRCNN(nn.Module):
    def __init__(self, num_channels=1):
        super(SRCNN, self).__init__()
        self.conv1 = nn.Conv2d(num_channels, 64, kernel_size=9, padding=2, padding_mode='replicate')
        self.conv2 = nn.Conv2d(64, 32, kernel_size=1, padding=2, padding_mode='replicate')
        self.conv3 = nn.Conv2d(32, num_channels, kernel_size=5, padding=2, padding_mode='replicate')

    def forward(self, x):
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = self.conv3(x)
        return x

# Test the model
print("1. Testing SRCNN Model Architecture...")
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"   Using device: {device}")

# Create model
model = SRCNN(num_channels=3)  # RGB images
model = model.to(device)
print("   ✓ SRCNN model created successfully")

# Test with dummy data
batch_size = 2
height, width = 216, 216  # Low resolution input size
dummy_input = torch.randn(batch_size, 3, height, width).to(device)
print(f"   ✓ Created dummy input: {dummy_input.shape}")

# Forward pass
with torch.no_grad():
    output = model(dummy_input)
    print(f"   ✓ Forward pass successful, output shape: {output.shape}")

print("\n2. Testing Loss Function...")
criterion = nn.MSELoss()
print("   ✓ MSE Loss function created")

print("\n3. Testing Optimizer...")
optimizer = torch.optim.Adam([
    {'params': model.conv1.parameters()},
    {'params': model.conv2.parameters()},
    {'params': model.conv3.parameters(), 'lr': 1e-4 * 0.1}
], lr=1e-4)
print("   ✓ Adam optimizer created successfully")

print("\n4. Testing Training Step...")
model.train()
# Create target with gradient tracking
target = torch.randn_like(dummy_input).to(device)
output = model(dummy_input)
loss = criterion(output, target)
print(f"   ✓ Loss calculated: {loss.item():.6f}")

optimizer.zero_grad()
loss.backward()
optimizer.step()
print("   ✓ Training step completed successfully")

print("\n5. Testing Model Save/Load...")
# Save model
weight_dir = "weight_srcnn"
if not os.path.exists(weight_dir):
    os.makedirs(weight_dir)

torch.save(model.state_dict(), os.path.join(weight_dir, 'demo.pth'))
print("   ✓ Model saved successfully")

# Load model
model_loaded = SRCNN(num_channels=3).to(device)
model_loaded.load_state_dict(torch.load(os.path.join(weight_dir, 'demo.pth'), map_location=device))
model_loaded.eval()
print("   ✓ Model loaded successfully")

print("\n6. Testing Evaluation Mode...")
with torch.no_grad():
    model_loaded.eval()
    test_output = model_loaded(dummy_input)
    print(f"   ✓ Evaluation successful, output shape: {test_output.shape}")

print("\n7. Model Summary...")
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
print(f"   Total parameters: {total_params:,}")
print(f"   Trainable parameters: {trainable_params:,}")

print("\n" + "=" * 40)
print("✅ SRCNN Demo completed successfully!")
print("\nNext Steps:")
print("1. Download DOTA dataset for training")
print("2. Run Dataset_Preprocessing.ipynb to prepare data")
print("3. Run SRCNN.ipynb for full training")
print("4. Try other models: LAPSRN, RCAN, SRGAN")
