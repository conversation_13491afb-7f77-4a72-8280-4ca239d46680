{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### SRCNN\n", "\n", "This notebook implements SRCNN model along with training and test data creation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Import Library\n", "\"\"\"\n", "from torch import nn\n", "import torch\n", "import numpy as np\n", "import glob\n", "from PIL import Image\n", "import os\n", "import h5py\n", "from torch.utils.data import Dataset\n", "import torch.optim as optim\n", "from torch.utils.data.dataloader import DataLoader\n", "import copy\n", "from tqdm import tqdm\n", "from pytorch_ssim import pytorch_ssim"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "SRCNN model\n", "\"\"\"\n", "class SRCNN(nn.Module):\n", "    def __init__(self, num_channels=1):\n", "        super(SRC<PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(num_channels, 64, kernel_size=9, padding=9 // 2)\n", "        self.conv2 = nn.Conv2d(64, 32, kernel_size=5, padding=5 // 2)\n", "        self.conv3 = nn.Conv2d(32, num_channels, kernel_size=5, padding=5 // 2)\n", "        self.relu = nn.ReLU(inplace=True)\n", "\n", "    def forward(self, x):\n", "        x = self.relu(self.conv1(x))\n", "        x = self.relu(self.conv2(x))\n", "        x = self.conv3(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Utility Function\n", "Helper function to convert from RGB to Y\n", "\"\"\"\n", "def convert_rgb_to_y(img):\n", "    return 16. + (64.738 * img[:, :, 0] + 129.057 * img[:, :, 1] + 25.064 * img[:, :, 2]) / 256.\n", "\n", "\"\"\"\n", "Convert RGB to YCbCr\n", "\"\"\"\n", "def convert_rgb_to_ycbcr(img):\n", "    y = 16. + (64.738 * img[:, :, 0] + 129.057 * img[:, :, 1] + 25.064 * img[:, :, 2]) / 256.\n", "    cb = 128. + (-37.945 * img[:, :, 0] - 74.494 * img[:, :, 1] + 112.439 * img[:, :, 2]) / 256.\n", "    cr = 128. + (112.439 * img[:, :, 0] - 94.154 * img[:, :, 1] - 18.285 * img[:, :, 2]) / 256.\n", "    return np.array([y, cb, cr]).transpose([1, 2, 0])\n", "\n", "\n", "\"\"\"\n", "Convert YCbCr to RGB\n", "\"\"\"\n", "def convert_ycbcr_to_rgb(img):\n", "    r = 298.082 * img[:, :, 0] / 256. + 408.583 * img[:, :, 2] / 256. - 222.921\n", "    g = 298.082 * img[:, :, 0] / 256. - 100.291 * img[:, :, 1] / 256. - 208.120 * img[:, :, 2] / 256. + 135.576\n", "    b = 298.082 * img[:, :, 0] / 256. + 516.412 * img[:, :, 1] / 256. - 276.836\n", "    return np.array([r, g, b]).transpose([1, 2, 0])\n", "\n", "\"\"\"\n", "Calculate PSNR\n", "\"\"\"\n", "def calc_psnr(img1, img2):\n", "    return 10. * torch.log10(1. / torch.mean((img1 - img2) ** 2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Create dataset using h5 format\n", "\"\"\"\n", "def create_data(path, output):\n", "\n", "    h5_file = h5py.File(os.path.join(path, output), 'w')\n", "\n", "    hr_image_path = os.path.join(path, 'images_stage3/*.png')\n", "    lr_image_path = os.path.join(path, 'images_stage4/*.png')\n", "\n", "    hr_image_list = glob.glob(hr_image_path)\n", "    lr_image_list = glob.glob(lr_image_path)\n", "\n", "    hr_imgs = []\n", "    lr_imgs = []\n", "\n", "    for i in range(len(hr_image_list)):\n", "\n", "        # open image\n", "        hr = Image.open(hr_image_list[i]).convert('RGB')\n", "        lr = Image.open(lr_image_list[i]).convert('RGB')\n", "\n", "        # convert data type\n", "        hr = np.array(hr).astype(np.float32)\n", "        lr = np.array(lr).astype(np.float32)\n", "\n", "        # convert rgb to y\n", "        hr = convert_rgb_to_y(hr)\n", "        lr = convert_rgb_to_y(lr)\n", "\n", "        hr_imgs.append(hr)\n", "        lr_imgs.append(lr)\n", "\n", "    hr_imgs = np.array(hr_imgs)\n", "    lr_imgs = np.array(lr_imgs)\n", "\n", "    h5_file.create_dataset('lr', np.shape(lr_imgs), h5py.h5t.STD_U8BE, data=lr_imgs)\n", "    h5_file.create_dataset('hr', np.shape(hr_imgs), h5py.h5t.STD_U8BE, data=hr_imgs)\n", "\n", "    h5_file.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["create_data('dataset/train', 'train_full.h5')\n", "create_data('dataset/val', 'val_full.h5')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Dataset feeding\n", "\"\"\"\n", "class CustomDataset(Dataset):\n", "    def __init__(self, h5_file):\n", "        super(CustomDataset, self).__init__()\n", "        self.h5_file = h5_file\n", "\n", "    def __getitem__(self, idx):\n", "        with h5py.File(self.h5_file, 'r') as f:\n", "            return np.expand_dims(f['lr'][idx] / 255., 0), np.expand_dims(f['hr'][idx] / 255., 0)\n", "\n", "    def __len__(self):\n", "        with h5py.File(self.h5_file, 'r') as f:\n", "            return len(f['lr'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Setup the model\n", "\"\"\"\n", "device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')\n", "torch.manual_seed(123)\n", "\n", "model = SRCNN().to(device)\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>([\n", "        {'params': model.conv1.parameters()},\n", "        {'params': model.conv2.parameters()},\n", "        {'params': model.conv3.parameters(), 'lr': 1e-4 * 0.1}\n", "    ], lr=1e-4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Setup data loder\n", "\"\"\"\n", "batch_size = 4\n", "\n", "train_dataset = CustomDataset('dataset/train/train_full.h5')\n", "train_dataloader = DataLoader(dataset=train_dataset,\n", "                                  batch_size=batch_size,\n", "                                  shuffle=True,\n", "                                  num_workers=0,\n", "                                  pin_memory=True,\n", "                                  drop_last=True)\n", "eval_dataset = CustomDataset('dataset/val/val_full.h5')\n", "eval_dataloader = DataLoader(dataset=eval_dataset, batch_size=batch_size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Util function to measure error\n", "\"\"\"\n", "class AverageMeter(object):\n", "    def __init__(self):\n", "        self.reset()\n", "\n", "    def reset(self):\n", "        self.val = 0\n", "        self.avg = 0\n", "        self.sum = 0\n", "        self.count = 0\n", "\n", "    def update(self, val, n=1):\n", "        self.val = val\n", "        self.sum += val * n\n", "        self.count += n\n", "        self.avg = self.sum / self.count"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Train and val the model\n", "\"\"\"\n", "best_weights = copy.deepcopy(model.state_dict())\n", "best_epoch = 0\n", "best_psnr = 0.0\n", "\n", "num_epoch = 20\n", "\n", "for epoch in range(num_epoch):\n", "    model.train()\n", "    epoch_losses = AverageMeter()\n", "\n", "    with tqdm(total=(len(train_dataset) - len(train_dataset) % batch_size)) as t:\n", "        t.set_description('epoch: {}/{}'.format(epoch, num_epoch - 1))\n", "        \n", "        # training\n", "        for data in train_dataloader:\n", "            inputs, labels = data\n", "\n", "            inputs = inputs.to(device, dtype=torch.float)\n", "            labels = labels.to(device, dtype=torch.float)\n", "\n", "            preds = model(inputs)\n", "            loss = criterion(preds, labels)\n", "            epoch_losses.update(loss.item(), len(inputs))\n", "\n", "            optimizer.zero_grad()\n", "            loss.backward()\n", "            optimizer.step()\n", "\n", "            t.set_postfix(loss='{:.6f}'.format(epoch_losses.avg))\n", "            t.update(len(inputs))\n", "\n", "        torch.save(model.state_dict(), os.path.join('weight_srcnn', 'epoch_{}.pth'.format(epoch)))\n", "        \n", "        # validation\n", "        model.eval()\n", "        epoch_psnr = AverageMeter()\n", "\n", "        for data in eval_dataloader:\n", "            inputs, labels = data\n", "\n", "            inputs = inputs.to(device, dtype=torch.float)\n", "            labels = labels.to(device, dtype=torch.float)\n", "\n", "            with torch.no_grad():\n", "                preds = model(inputs).clamp(0.0, 1.0)\n", "\n", "            epoch_psnr.update(calc_psnr(preds, labels), len(inputs))\n", "\n", "        print('eval psnr: {:.2f}'.format(epoch_psnr.avg))\n", "\n", "        # save best weight\n", "        if epoch_psnr.avg > best_psnr:\n", "            best_epoch = epoch\n", "            best_psnr = epoch_psnr.avg\n", "            best_weights = copy.deepcopy(model.state_dict())\n", "\n", "    print('best epoch: {}, psnr: {:.2f}'.format(best_epoch, best_psnr))\n", "    torch.save(best_weights, os.path.join('weight_srcnn', 'best.pth'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Evaluate the model with test set\n", "\"\"\"\n", "\n", "# setup to GPU if available\n", "device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')\n", "\n", "# initialize model with best weight\n", "model = SRCNN().to(device)\n", "state_dict = model.state_dict()\n", "for n, p in torch.load('weight_srcnn/best.pth', map_location=lambda storage, loc: storage).items():\n", "    if n in state_dict.keys():\n", "        state_dict[n].copy_(p)\n", "    else:\n", "        raise KeyError(n)\n", "\n", "# set model to evaluation mode\n", "model.eval()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["lr_image_path = 'dataset/test/images_stage4/*.png'\n", "lr_image_list = glob.glob(lr_image_path)\n", "hr_image_path = 'dataset/test/images_stage3/*.png'\n", "hr_image_list = glob.glob(hr_image_path)\n", "\n", "psnr_total = 0\n", "ssim_total = 0\n", "\n", "for i, img in enumerate(lr_image_list):\n", "    image = Image.open(img).convert('RGB')\n", "    image = np.array(image).astype(np.float32)\n", "    ycbcr = convert_rgb_to_ycbcr(image)\n", "\n", "    # perform image transformation\n", "    y = ycbcr[..., 0]\n", "    y /= 255.\n", "    y = torch.from_numpy(y).to(device)\n", "    y = y.unsqueeze(0).unsqueeze(0)\n", "\n", "    label = Image.open(hr_image_list[i]).convert('RGB')\n", "    label = np.array(label).astype(np.float32)\n", "\n", "    y_l = label[..., 0]\n", "    y_l /= 255.\n", "    y_l = torch.from_numpy(y_l).to(device)\n", "    y_l = y_l.unsqueeze(0).unsqueeze(0)\n", "\n", "    with torch.no_grad():\n", "        preds = model(y).clamp(0.0, 1.0)\n", "\n", "    psnr = calc_psnr(y_l, preds)\n", "    psnr_total += psnr\n", "    ssim = pytorch_ssim.ssim(y_l, preds)\n", "    ssim_total += ssim\n", "    print('PSNR: {:.2f}'.format(psnr))\n", "    print('SSIM: {:.2f}'.format(ssim))\n", "\n", "    # inverse transform and save images\n", "    preds = preds.mul(255.0).cpu().numpy().squeeze(0).squeeze(0)\n", "    output = np.array([preds, ycbcr[..., 1], ycbcr[..., 2]]).transpose([1, 2, 0])\n", "    output = np.clip(convert_ycbcr_to_rgb(output), 0.0, 255.0).astype(np.uint8)\n", "    output = Image.fromarray(output)\n", "    output.save(f'result_srcnn_new/img_{i}.png')\n", "\n", "psnr_total /= len(lr_image_list)\n", "ssim_total /= len(lr_image_list)\n", "print('PSNR_T: {:.4f}'.format(psnr_total))\n", "print('SSIM_T: {:.4f}'.format(ssim_total))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 1}