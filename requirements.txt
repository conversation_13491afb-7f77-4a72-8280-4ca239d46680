# Core Deep Learning Framework
torch>=1.9.0
torchvision>=0.10.0

# Image Processing
Pillow>=8.0.0
opencv-python>=4.5.0

# Data Handling
numpy>=1.21.0
pandas>=1.3.0
h5py>=3.1.0

# Progress Bars and Utilities
tqdm>=4.62.0

# SSIM Calculation (Custom package needed for SRCNN)
pytorch-ssim

# Scientific Computing
scipy>=1.7.0

# Jupyter Notebook Support
jupyter>=1.0.0
ipykernel>=6.0.0

# Plotting and Visualization
matplotlib>=3.4.0

# Additional utilities
glob2>=0.7
