{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### LAPSRN\n", "\n", "This notebook implements LAPSRN model along with training and test data creation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Import Library\n", "\"\"\"\n", "from torch import nn\n", "import h5py\n", "import numpy as np\n", "import glob\n", "import os\n", "from PIL import Image\n", "from torch.utils.data import Dataset\n", "import torch.optim as optim\n", "from torch.utils.data.dataloader import DataLoader\n", "import torch\n", "from tqdm import tqdm\n", "from collections import namedtuple\n", "import copy\n", "import math"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Utility function\n", "\"\"\"\n", "def get_upsample_filter(size):\n", "    #Make a 2D bilinear kernel suitable for upsampling\n", "    factor = (size + 1) // 2\n", "    if size % 2 == 1:\n", "        center = factor - 1\n", "    else:\n", "        center = factor - 0.5\n", "    og = np.ogrid[:size, :size]\n", "    filter = (1 - abs(og[0] - center) / factor) * \\\n", "             (1 - abs(og[1] - center) / factor)\n", "    return torch.from_numpy(filter).float()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "LAPSRN model\n", "\"\"\"\n", "class ConvBlock(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.cov_block = nn.Sequential(\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.ConvTranspose2d(in_channels=64, out_channels=64, kernel_size=4, stride=2, padding=1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "        )\n", "\n", "    def forward(self, x):\n", "        output = self.cov_block(x)\n", "        return output\n", "\n", "class LAPSRN(nn.Module):\n", "    def __init__(self):\n", "        super(LAPSR<PERSON>, self).__init__()\n", "\n", "        self.conv_input = nn.Conv2d(in_channels=3, out_channels=64, kernel_size=3, stride=1, padding=1, bias=False)\n", "        self.relu = nn.LeakyReLU(0.2, inplace=True)\n", "\n", "        self.convt_I1 = nn.ConvTranspose2d(in_channels=3, out_channels=3, kernel_size=4, stride=2, padding=1, bias=False)\n", "        self.convt_R1 = nn.Conv2d(in_channels=64, out_channels=3, kernel_size=3, stride=1, padding=1, bias=False)\n", "        self.convt_F1 = self.make_layer(ConvBlock)\n", "\n", "        self.convt_I2 = nn.ConvTranspose2d(in_channels=3, out_channels=3, kernel_size=4, stride=2, padding=1, bias=False)\n", "        self.convt_R2 = nn.Conv2d(in_channels=64, out_channels=3, kernel_size=3, stride=1, padding=1, bias=False)\n", "        self.convt_F2 = self.make_layer(ConvBlock)\n", "\n", "        for m in self.modules():\n", "            if isinstance(m, nn.Conv2d):\n", "                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels\n", "                m.weight.data.normal_(0, math.sqrt(2. / n))\n", "                if m.bias is not None:\n", "                    m.bias.data.zero_()\n", "            if isinstance(m, nn.ConvTranspose2d):\n", "                c1, c2, h, w = m.weight.data.size()\n", "                weight = get_upsample_filter(h)\n", "                m.weight.data = weight.view(1, 1, h, w).repeat(c1, c2, 1, 1)\n", "                if m.bias is not None:\n", "                    m.bias.data.zero_()\n", "\n", "    def make_layer(self, block):\n", "        layers = []\n", "        layers.append(block())\n", "        return nn.Sequential(*layers)\n", "\n", "    def forward(self, x):\n", "        out = self.relu(self.conv_input(x))\n", "\n", "        convt_F1 = self.convt_F1(out)\n", "        convt_I1 = self.convt_I1(x)\n", "        convt_R1 = self.convt_R1(convt_F1)\n", "        HR_2x = convt_I1 + convt_R1\n", "\n", "        convt_F2 = self.convt_F2(convt_F1)\n", "        convt_I2 = self.convt_I2(HR_2x)\n", "        convt_R2 = self.convt_R2(convt_F2)\n", "        HR_4x = convt_I2 + convt_R2\n", "\n", "        return HR_2x, HR_4x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Loss function\n", "\"\"\"\n", "class L1_Charbonnier_loss(nn.<PERSON><PERSON><PERSON>):\n", "    \"\"\"L1 Charbonnierloss.\"\"\"\n", "    def __init__(self):\n", "        super(L1_<PERSON><PERSON>_loss, self).__init__()\n", "        self.eps = 1e-6\n", "\n", "    def forward(self, X, Y):\n", "        diff = torch.add(X, -Y)\n", "        error = torch.sqrt( diff * diff + self.eps )\n", "        loss = torch.sum(error)\n", "        return loss"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Setup the dataset\n", "\"\"\"\n", "def create_data(path, output):\n", "\n", "    h5_file = h5py.File(os.path.join(path, output), 'w')\n", "\n", "    hr_image_path = os.path.join(path, 'images_stage3/*.png')\n", "    lr_image_path = os.path.join(path, 'images_stage5/*.png')\n", "    mid_image_path = os.path.join(path, 'images_stage6/*.png')\n", "\n", "    hr_image_list = glob.glob(hr_image_path)\n", "    lr_image_list = glob.glob(lr_image_path)\n", "    mid_image_list = glob.glob(mid_image_path)\n", "\n", "    hr_imgs = []\n", "    lr_imgs = []\n", "    mid_imgs = []\n", "\n", "    for i in range(len(hr_image_list)):\n", "\n", "        # open image\n", "        hr = Image.open(hr_image_list[i]).convert('RGB')\n", "        lr = Image.open(lr_image_list[i]).convert('RGB')\n", "        mid = Image.open(mid_image_list[i]).convert('RGB')\n", "\n", "        # convert data type\n", "        hr = np.array(hr).astype(np.float32)\n", "        lr = np.array(lr).astype(np.float32)\n", "        mid = np.array(mid).astype(np.float32)\n", "\n", "        # transpose and normalize\n", "        hr = np.transpose(hr, axes=[2, 0, 1])\n", "        lr = np.transpose(lr, axes=[2, 0, 1])\n", "        mid = np.transpose(mid, axes=[2, 0, 1])\n", "\n", "        hr /= 255.0\n", "        lr /= 255.0\n", "        mid /= 255.0\n", "\n", "        hr_imgs.append(hr)\n", "        lr_imgs.append(lr)\n", "        mid_imgs.append(mid)\n", "\n", "    hr_imgs = np.array(hr_imgs)\n", "    lr_imgs = np.array(lr_imgs)\n", "    mid_imgs = np.array(mid_imgs)\n", "\n", "    h5_file.create_dataset('lr', np.shape(lr_imgs), h5py.h5t.IEEE_F32LE, data=lr_imgs)\n", "    h5_file.create_dataset('hr', np.shape(hr_imgs), h5py.h5t.IEEE_F32LE, data=hr_imgs)\n", "    h5_file.create_dataset('mid', np.shape(mid_imgs), h5py.h5t.IEEE_F32LE, data=mid_imgs)\n", "\n", "    h5_file.close()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["create_data('dataset/train', 'train_full_mid.h5')\n", "create_data('dataset/val', 'val_full_mid.h5')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Dataset feeding\n", "\"\"\"\n", "class CustomDataset(Dataset):\n", "    def __init__(self, h5_file):\n", "        super(CustomDataset, self).__init__()\n", "        self.h5_file = h5_file\n", "\n", "    def __getitem__(self, idx):\n", "        with h5py.File(self.h5_file, 'r') as f:\n", "            return f['lr'][idx], f['hr'][idx], f['mid'][idx]\n", "\n", "    def __len__(self):\n", "        with h5py.File(self.h5_file, 'r') as f:\n", "            return len(f['lr'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Model Setup\n", "\"\"\"\n", "torch.manual_seed(123)\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "batch_size = 2\n", "\n", "model = LAPSRN().to(device)\n", "criterion = L1_Charbonnier_loss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=1e-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Setup data loader\n", "\"\"\"\n", "train_dataset = CustomDataset('dataset/train/train_full_mid.h5')\n", "train_dataloader = DataLoader(dataset=train_dataset,\n", "                                  batch_size=batch_size,\n", "                                  shuffle=True,\n", "                                  num_workers=0,\n", "                                  pin_memory=True,\n", "                                  drop_last=True)\n", "eval_dataset = CustomDataset('dataset/val/val_full_mid.h5')\n", "eval_dataloader = DataLoader(dataset=eval_dataset, batch_size=batch_size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Util function to measure error\n", "\"\"\"\n", "class AverageMeter(object):\n", "    def __init__(self):\n", "        self.reset()\n", "\n", "    def reset(self):\n", "        self.val = 0\n", "        self.avg = 0\n", "        self.sum = 0\n", "        self.count = 0\n", "\n", "    def update(self, val, n=1):\n", "        self.val = val\n", "        self.sum += val * n\n", "        self.count += n\n", "        self.avg = self.sum / self.count\n", "\n", "\"\"\"\n", "Calculate PSNR\n", "\"\"\"\n", "def calc_psnr(img1, img2):\n", "    return 10. * torch.log10(1. / torch.mean((img1 - img2) ** 2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Train and val the model\n", "\"\"\"\n", "best_weights = copy.deepcopy(model.state_dict())\n", "best_epoch = 0\n", "best_psnr = 0.0\n", "\n", "num_epoch = 20\n", "\n", "for epoch in range(num_epoch):\n", "    model.train()\n", "    epoch_losses = AverageMeter()\n", "\n", "    with tqdm(total=(len(train_dataset) - len(train_dataset) % batch_size)) as t:\n", "        t.set_description('epoch: {}/{}'.format(epoch, num_epoch - 1))\n", "\n", "        # adjust learning rate\n", "        lr =  1e-3 * (0.1 ** (epoch // 5))\n", "        for param_group in optimizer.param_groups:\n", "            param_group[\"lr\"] = lr\n", "        \n", "        # training\n", "        for data in train_dataloader:\n", "            inputs, labels, mid_labels = data\n", "\n", "            inputs = inputs.to(device, dtype=torch.float)\n", "            labels = labels.to(device, dtype=torch.float)\n", "            mid_labels = mid_labels.to(device, dtype=torch.float)\n", "\n", "            pred_mid, pred = model(inputs)\n", "\n", "            loss_x2 = criterion(pred_mid, mid_labels)\n", "            loss_x4 = criterion(pred, labels)\n", "            loss = loss_x2 + loss_x4\n", "\n", "            epoch_losses.update(loss.item(), len(inputs))\n", "\n", "            optimizer.zero_grad()\n", "            loss_x2.backward(retain_graph=True)\n", "            loss_x4.backward()\n", "            optimizer.step()\n", "\n", "            t.set_postfix(loss='{:.6f}'.format(epoch_losses.avg))\n", "            t.update(len(inputs))\n", "\n", "        torch.save(model.state_dict(), os.path.join('weight_lapsrn', 'epoch_{}.pth'.format(epoch)))\n", "\n", "        # validation\n", "        model.eval()\n", "        epoch_psnr = AverageMeter()\n", "\n", "        for data in eval_dataloader:\n", "            inputs, labels, mid_labels = data\n", "\n", "            inputs = inputs.to(device, dtype=torch.float)\n", "            labels = labels.to(device, dtype=torch.float)\n", "\n", "            with torch.no_grad():\n", "                pred_mid, preds = model(inputs)\n", "                preds = torch.clamp(preds, 0.0, 1.0)\n", "\n", "            epoch_psnr.update(calc_psnr(preds, labels), len(inputs))\n", "\n", "        print('eval psnr: {:.2f}'.format(epoch_psnr.avg))\n", "\n", "        # save the best weight\n", "        if epoch_psnr.avg > best_psnr:\n", "            best_epoch = epoch\n", "            best_psnr = epoch_psnr.avg\n", "            best_weights = copy.deepcopy(model.state_dict())\n", "\n", "    print('best epoch: {}, psnr: {:.2f}'.format(best_epoch, best_psnr))\n", "    torch.save(best_weights, os.path.join('weight_lapsrn', 'best.pth'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Evaluate the model with test set\n", "\"\"\"\n", "device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')\n", "\n", "model = LAPSRN().to(device)\n", "state_dict = model.state_dict()\n", "for n, p in torch.load('weight_lapsrn/best.pth', map_location=lambda storage, loc: storage).items():\n", "    if n in state_dict.keys():\n", "        state_dict[n].copy_(p)\n", "    else:\n", "        raise KeyError(n)\n", "\n", "model.eval()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from pytorch_ssim import pytorch_ssim\n", "\n", "lr_image_path = 'dataset/test/images_stage5/*.png'\n", "lr_image_list = glob.glob(lr_image_path)\n", "hr_image_path = 'dataset/test/images_stage3/*.png'\n", "hr_image_list = glob.glob(hr_image_path)\n", "\n", "psnr_total = 0\n", "ssim_total = 0\n", "\n", "for i, img in enumerate(lr_image_list):\n", "    image = Image.open(img).convert('RGB')\n", "    image = np.array(image).astype(np.float32)\n", "    image = np.transpose(image, axes=[2, 0, 1])\n", "    image /= 255.0\n", "\n", "    image = torch.from_numpy(image).to(device)\n", "    image = image.unsqueeze(0)\n", "\n", "    label = Image.open(hr_image_list[i]).convert('RGB')\n", "    label = np.array(label).astype(np.float32)\n", "    label = np.transpose(label, axes=[2, 0, 1])\n", "    label /= 255.0\n", "    label = torch.from_numpy(label).to(device)\n", "    label = label.unsqueeze(0)\n", "\n", "    with torch.no_grad():\n", "        pred_mid, preds = model(image)\n", "        preds = torch.clamp(preds, 0.0, 1.0)\n", "\n", "    psnr = calc_psnr(label, preds)\n", "    psnr_total += psnr\n", "    ssim = pytorch_ssim.ssim(label, preds)\n", "    ssim_total += ssim\n", "    print('PSNR: {:.2f}'.format(psnr))\n", "    print('SSIM: {:.2f}'.format(ssim))\n", "\n", "    output = preds.mul_(255.0).clamp_(0.0, 255.0).squeeze(0).permute(1, 2, 0).byte().cpu().numpy()\n", "    output = Image.fromarray(output)\n", "    output.save(f'result_lapsrn_new/img_{i}.png')\n", "\n", "psnr_total /= len(lr_image_list)\n", "ssim_total /= len(lr_image_list)\n", "print('PSNR_T: {:.4f}'.format(psnr_total))\n", "print('SSIM_T: {:.4f}'.format(ssim_total))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 1}