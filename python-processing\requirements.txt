# Deep Learning Framework
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Image Processing
opencv-python>=4.8.0
Pillow>=10.0.0
scikit-image>=0.21.0
imageio>=2.31.0

# Quality Assessment
pyiqa>=0.1.7
lpips>=0.1.4

# Scientific Computing
numpy>=1.24.0
scipy>=1.11.0
pandas>=2.0.0

# Machine Learning
scikit-learn>=1.3.0

# Image Registration & Alignment
imreg_dft>=2.0.0
pystackreg>=0.2.7

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Progress Bars
tqdm>=4.65.0

# File I/O
h5py>=3.9.0
json5>=0.9.0

# Web Framework Integration
flask>=2.3.0
flask-cors>=4.0.0

# Utilities
argparse
pathlib
glob2>=0.7
psutil>=5.9.0

# Development Tools
jupyter>=1.0.0
ipykernel>=6.25.0

# Optional: CUDA support (uncomment if GPU available)
# torch-audio
# torchtext
