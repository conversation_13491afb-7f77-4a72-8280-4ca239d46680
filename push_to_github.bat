@echo off
echo ========================================
echo  SatelliteSR - GitHub Push Script
echo ========================================
echo.

echo Step 1: Checking Git status...
git status
echo.

echo Step 2: Adding all files...
git add .
echo.

echo Step 3: Committing changes...
git commit -m "Updated SatelliteSR project with latest changes"
echo.

echo Step 4: Pushing to GitHub...
echo Make sure you have created the repository on GitHub first!
echo Repository URL: https://github.com/RajatBanshiwal/Satellite-SR.git
echo.

git push -u origin main

echo.
echo ========================================
echo Push completed! Check your GitHub repo.
echo ========================================
pause
