#!/usr/bin/env python3
"""
Test script to verify all dependencies are working correctly
"""

print("Testing SatelliteSR Environment Setup...")
print("=" * 50)

# Test basic imports
try:
    import torch
    print(f"✓ PyTorch: {torch.__version__}")
    print(f"  - CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  - CUDA Device: {torch.cuda.get_device_name(0)}")
    else:
        print("  - Running on CPU")
except ImportError as e:
    print(f"✗ PyTorch import failed: {e}")

try:
    import torchvision
    print(f"✓ TorchVision: {torchvision.__version__}")
except ImportError as e:
    print(f"✗ TorchVision import failed: {e}")

try:
    import numpy as np
    print(f"✓ NumPy: {np.__version__}")
except ImportError as e:
    print(f"✗ NumPy import failed: {e}")

try:
    import pandas as pd
    print(f"✓ Pandas: {pd.__version__}")
except ImportError as e:
    print(f"✗ Pandas import failed: {e}")

try:
    from PIL import Image
    print("✓ PIL (Pillow) imported successfully")
except ImportError as e:
    print(f"✗ PIL import failed: {e}")

try:
    import h5py
    print(f"✓ h5py: {h5py.__version__}")
except ImportError as e:
    print(f"✗ h5py import failed: {e}")

try:
    import cv2
    print(f"✓ OpenCV: {cv2.__version__}")
except ImportError as e:
    print(f"✗ OpenCV import failed: {e}")

try:
    from tqdm import tqdm
    print("✓ tqdm imported successfully")
except ImportError as e:
    print(f"✗ tqdm import failed: {e}")

try:
    from pytorch_ssim import pytorch_ssim
    print("✓ pytorch_ssim imported successfully")
except ImportError as e:
    print(f"✗ pytorch_ssim import failed: {e}")

try:
    import matplotlib.pyplot as plt
    print("✓ Matplotlib imported successfully")
except ImportError as e:
    print(f"✗ Matplotlib import failed: {e}")

try:
    import scipy
    print(f"✓ SciPy: {scipy.__version__}")
except ImportError as e:
    print(f"✗ SciPy import failed: {e}")

print("\n" + "=" * 50)

# Test basic PyTorch functionality
try:
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"✓ Device set to: {device}")
    
    # Create a simple tensor
    x = torch.randn(2, 3, 4, 4).to(device)
    print(f"✓ Created test tensor with shape: {x.shape}")
    
    # Test basic neural network layer
    conv = torch.nn.Conv2d(3, 16, 3, padding=1).to(device)
    y = conv(x)
    print(f"✓ Conv2d layer test passed, output shape: {y.shape}")
    
except Exception as e:
    print(f"✗ PyTorch functionality test failed: {e}")

print("\n" + "=" * 50)
print("Environment test completed!")
print("If all items show ✓, your environment is ready for SatelliteSR!")
