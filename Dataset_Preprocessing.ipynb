{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Prepare dataset\n", "\n", "As DOTA dataset is originally prepared for object detection/classification tasks, it is required to preprocess the data to be used for super-resolution task. This notebook converts the data into useable format."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import glob\n", "from shutil import copyfile, copytree\n", "from PIL import Image\n", "import os\n", "from random import randrange"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Read label and extract GSD\n", "\"\"\"\n", "files = glob.glob(\"dataset/train/labelTxt/*.txt\")\n", "GSD = []\n", "file_name = []\n", "for file in files:\n", "    with open(file, 'r') as f:\n", "        file_name.append(file.split('\\\\')[-1])\n", "        f.readline() # ignore first line\n", "        txt = f.readline()\n", "        if \"null\" not in txt:\n", "            GSD.append(float(txt[4:-1]))\n", "        else:\n", "            GSD.append(0.0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Create pandas DataFrame for visualization\n", "\"\"\"\n", "df = pd.DataFrame({'filename': file_name, 'GSD': GSD})\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["df.plot.hist(bins=50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Identify the images whose GSD is between 0.1 and 0.15 to have images with similar height\n", "\"\"\"\n", "df1 = df[df['GSD'] < 0.15]\n", "df2 = df1[df1['GSD'] > 0.1]\n", "df2.plot.hist(bins=10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["df2.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Extract image and labels that are meeting the criteria above.\n", "\"\"\"\n", "for index, row in df2.iterrows():\n", "    copyfile('dataset/train/images/'+row['filename'].replace('txt', 'png'), 'dataset/train/images_stage1/'+row['filename'].replace('txt', 'png'))\n", "    copyfile('dataset/train/labelTxt/'+row['filename'], 'dataset/train/labelTxt_stage1/'+row['filename'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["df2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Crop the image so that all images have same resolution\n", "\"\"\"\n", "def crop(path, file, height, width):\n", "    im = Image.open(file)\n", "    filename = file.split('\\\\')[-1]\n", "    filename = filename[:-4]\n", "    imgwidth, imgheight = im.size\n", "    k = 0\n", "    for i in range(30,imgheight-30,height):\n", "        for j in range(30,imgwidth-30,width):\n", "            if j+width > imgwidth or i+height > imgheight:\n", "                continue\n", "            box = (j, i, j+width, i+height)\n", "            a = im.crop(box)\n", "            a.save(os.path.join(path, f\"{filename}-{k}.png\"))\n", "            k += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Perform cropping\n", "\"\"\"\n", "images_st1 = glob.glob(\"dataset/train/images_stage1/*.png\")\n", "for img in images_st1:\n", "    crop('dataset/train/images_stage2', img, 1024, 1024)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Go through image and remove images where black area is included\n", "We consider image are invalid if any 4 corner of image is black\n", "\"\"\"\n", "# copy to stage 3\n", "copytree('dataset/train/images_stage2','dataset/train/images_stage3')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["images_st3 = glob.glob(\"dataset/train/images_stage3/*.png\")\n", "for img in images_st3:\n", "    image = Image.open(img)\n", "    width, height = image.size\n", "    loc = ((0,0), (width-1,0), (0,height-1), (width-1,height-1))\n", "    for x,y in loc:\n", "        if sum(image.getpixel((x, y))) < 5:\n", "            os.remove(img)\n", "            break"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Present some of high resolution images\n", "\"\"\"\n", "image_path = 'dataset/train/images_stage3/P0038-3.png'\n", "image = Image.open(image_path)\n", "image.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Create low resolution image out of high resolution images\n", "\"\"\"\n", "reduced_512 = image.resize((512,512), resample=Image.BICUBIC)\n", "reduced_512.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["upsample_512 = reduced_512.resize((1024,1024), resample=Image.BICUBIC)\n", "upsample_512.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["reduced_216 = image.resize((216,216), resample=Image.BICUBIC)\n", "reduced_216.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["upsample_256 = reduced_216.resize((1024,1024), resample=Image.BICUBIC)\n", "upsample_256.show()\n", "\n", "#\n", "# We will downsample to 216, and upsample to create low resolution image\n", "#\n", "NEAREST_256 = reduced_216.resize((1024,1024), resample=Image.NEAREST)\n", "upsample_256.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["BOX_256 = reduced_216.resize((1024,1024), resample=Image.BOX)\n", "upsample_256.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["BILINEAR_256 = reduced_216.resize((1024,1024), resample=Image.BILINEAR)\n", "upsample_256.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["HAMMING_256 = reduced_216.resize((1024,1024), resample=Image.HAMMING)\n", "upsample_256.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Create low resolution input 256x256\n", "Also, create 1024 low res image with bicubic\n", "\"\"\"\n", "images_st3 = glob.glob(\"dataset/train/images_stage3/*.png\")\n", "resample_method = [Image.BICUBIC, Image.NEAREST, Image.BOX, Image.BILINEAR, Image.HAMMING]\n", "for img in images_st3:\n", "    image = Image.open(img)\n", "    reduced_img = image.resize((256,256), resample=resample_method[randrange(5)])\n", "    reduced_img.save(img.replace('images_stage3', 'images_stage5'))\n", "    upsample_img = reduced_img.resize((1024,1024), resample=Image.BICUBIC)\n", "    upsample_img.save(img.replace('images_stage3', 'images_stage4'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\"\"\"\n", "Create 512 reference image for LAPSRN\n", "\"\"\"\n", "images_st3 = glob.glob(\"dataset/train/images_stage3/*.png\")\n", "resample_method = [Image.BICUBIC, Image.NEAREST, Image.BOX, Image.BILINEAR, Image.HAMMING]\n", "for img in images_st3:\n", "    image = Image.open(img)\n", "    mid_img = image.resize((512,512), resample=Image.BICUBIC)\n", "    mid_img.save(img.replace('images_stage3', 'images_stage6'))"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%%\n"}}, "source": ["Note that \n", "\n", "- images_stage3 => High Resolution Image\n", "- images_stage4 => Low Resolution 1024x1024\n", "- images_stage5 => Low Resolution 256x256\n", "- images_stage6 => High Resolution 512 Image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 1}